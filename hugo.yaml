# Hugo configuration file
title: BlockSmith

# import hextra as module
module:
  imports:
    - path: github.com/imfing/hextra
  hugoVersion:
    extended: true
    min: "0.134.0"

markup:
  # allow raw html
  goldmark:
    renderer:
      unsafe: true

  # enable hextra syntax highlight
  highlight:
    noClasses: true

menu:
  main:
    - name: About
      pageRef: /about
      weight: 2
    - name: Search
      weight: 4
      params:
        type: search
    - name: Discord
      weight: 5
      url: "https://discord.com/invite/kMUSvP92fh"
      params:
        icon: discord
  sidebar:
    - identifier: more
      name: More
      params:
        type: separator
      weight: 1
params:
  navbar:
    displayTitle: true
    displayLogo: false

  footer:
    displayCopyright: false
    displayPoweredBy: true

  editURL:
    enable: true
    base: "https://github.com/Starfruit2210/blocksmith/tree/main/content"
