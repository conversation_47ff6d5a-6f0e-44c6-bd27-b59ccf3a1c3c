---
title: Introduction
type: docs
prev: docs/nextannouncers/
weight: 1
---

![](logo.png)

Welcome to the documentation of NextAnnouncers!
A quick and optimized repeatable broadcast(announcers) to help server owner that has multiple server and doesn't want to change each backend broadcast everytime.

NextAnnouncers is a plugin designed for Server Owners that has too many backends broadcast making it quite a hassle to edit each one. But this plugin does it all in a single instance without any hassle whatsoever!

## Features
- Easy to use
- Repeatable Broadcast to all backends server
- Blacklist Server
- Playsound to all backends server
- Adjustable interval
- Customizeable Settings
- Customizeable Language Settings
- Turn on / off announcers

## Supported Backends
- Velocity

## Commands
> <> = Required [] = Optional

- /nextannouncers
- /nextannouncers <reload>

## Permissions
- nextannouncers.reload